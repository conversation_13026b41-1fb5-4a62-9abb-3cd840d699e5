import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../constants/app_theme.dart';
import '../models/trip_model.dart';
import '../pages/trip/trip_details_page.dart';
import '../utils/navigation_utils.dart';
import '../services/storage_service.dart';
import 'rating_stars.dart';
import 'verification_badge.dart';
import 'package:intl/intl.dart';

class TripCard extends StatelessWidget {
  final TripModel trip;
  final VoidCallback? onTap;
  final bool showBookButton;
  final bool isCompact;

  const TripCard({
    super.key,
    required this.trip,
    this.onTap,
    this.showBookButton = true,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      elevation: 3,
      shadowColor: AppColors.shadow,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(20),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Trip Type Badge and Price
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  _buildTripTypeBadge(),
                  _buildPriceTag(),
                ],
              ),

              const SizedBox(height: 16),

              // Route Information
              _buildRouteSection(),

              const SizedBox(height: 16),

              // Date, Time, and Seats Info
              _buildTripInfoRow(),

              const SizedBox(height: 16),

              // Driver Information
              _buildDriverSection(),

              const SizedBox(height: 16),

              // Car Information
              if (_hasCarInfo()) ...[
                _buildCarInfoSection(),
                const SizedBox(height: 12),
              ],

              // Amenities
              if (trip.amenities.isNotEmpty) ...[
                _buildAmenitiesSection(),
                const SizedBox(height: 12),
              ],

              // Rules and Notes
              if (trip.rules.isNotEmpty || (trip.notes != null && trip.notes!.isNotEmpty)) ...[
                _buildRulesAndNotesSection(),
                const SizedBox(height: 16),
              ],

              // View Details Button
              if (showBookButton) ...[
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: () {
                      if (onTap != null) {
                        onTap!();
                      } else {
                        _navigateToTripDetails(context);
                      }
                    },
                    icon: const Icon(Icons.visibility, size: 18),
                    label: const Text('عرض التفاصيل'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 14),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTripTypeBadge() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: _getTripTypeColor(trip.tripType).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: _getTripTypeColor(trip.tripType),
          width: 1.5,
        ),
      ),
      child: Text(
        _getTripTypeText(trip.tripType),
        style: TextStyle(
          color: _getTripTypeColor(trip.tripType),
          fontWeight: FontWeight.bold,
          fontSize: 12,
        ),
      ),
    );
  }

  Widget _buildPriceTag() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: AppColors.secondary,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            '${trip.price.toInt()} درهم',
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
          ),
          if (trip.isPriceNegotiable) ...[
            const SizedBox(width: 4),
            const Icon(
              Icons.handshake,
              color: Colors.white,
              size: 16,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildRouteSection() {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(Icons.location_on, color: AppColors.primary, size: 18),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      trip.fromCity,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  const Icon(Icons.flag, color: AppColors.secondary, size: 18),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      trip.toCity,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        const SizedBox(width: 16),
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: AppColors.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(30),
          ),
          child: const Icon(
            Icons.directions_car,
            color: AppColors.primary,
            size: 30,
          ),
        ),
      ],
    );
  }

  Widget _buildTripInfoRow() {
    final dateFormat = DateFormat('dd/MM', 'ar');
    return Row(
      children: [
        Expanded(
          child: _InfoItem(
            icon: Icons.calendar_today,
            label: dateFormat.format(trip.departureDate),
            color: AppColors.primary,
          ),
        ),
        Expanded(
          child: _InfoItem(
            icon: Icons.access_time,
            label: trip.departureTime,
            color: AppColors.secondary,
          ),
        ),
        Expanded(
          child: _InfoItem(
            icon: Icons.airline_seat_recline_normal,
            label: '${trip.availableSeats}/${trip.totalSeats}',
            color: AppColors.accent,
          ),
        ),
      ],
    );
  }

  Widget _buildDriverSection() {
    // Generate profile image URL using StorageService
    String? profileImageUrl;
    if (trip.leader != null) {
      profileImageUrl = StorageService.getProfileImageUrl(
        trip.leader!.id,
        storedUrl: trip.leader!.profileImageUrl,
      );
    }

    return Row(
      children: [
        Container(
          width: 44,
          height: 44,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(
              color: AppColors.primary.withValues(alpha: 0.3),
              width: 2,
            ),
          ),
          child: CircleAvatar(
            radius: 20,
            backgroundColor: AppColors.primary,
            backgroundImage: profileImageUrl != null && profileImageUrl.isNotEmpty
                ? CachedNetworkImageProvider(profileImageUrl)
                : null,
            child: profileImageUrl == null || profileImageUrl.isEmpty
                ? Text(
                    trip.leader?.fullName.substring(0, 1) ?? 'ق',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  )
                : null,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              VerifiedUserName(
                name: trip.leader?.fullName ?? 'قائد الرحلة',
                isVerified: trip.leader?.isVerified ?? false,
                textStyle: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
                badgeSize: 16.0,
              ),
              if (trip.leader != null) ...[
                const SizedBox(height: 4),
                Row(
                  children: [
                    RatingStars(
                      rating: trip.leader!.rating,
                      size: 14,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      trip.leader!.displayRating,
                      style: const TextStyle(
                        color: AppColors.textSecondary,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  bool _hasCarInfo() {
    return trip.carModel != null ||
           trip.carColor != null ||
           trip.carPlateNumber != null ||
           trip.carType != null ||
           trip.carPlate != null;
  }

  Widget _buildCarInfoSection() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.surfaceVariant,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          const Icon(Icons.directions_car, color: AppColors.primary, size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (trip.carModel != null || trip.carType != null)
                  Text(
                    trip.carModel ?? trip.carType ?? '',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 13,
                    ),
                  ),
                Row(
                  children: [
                    if (trip.carColor != null) ...[
                      Text(
                        trip.carColor!,
                        style: const TextStyle(
                          color: AppColors.textSecondary,
                          fontSize: 12,
                        ),
                      ),
                      if (trip.carPlateNumber != null || trip.carPlate != null) const Text(' • '),
                    ],
                    if (trip.carPlateNumber != null || trip.carPlate != null)
                      Text(
                        trip.carPlateNumber ?? trip.carPlate ?? '',
                        style: const TextStyle(
                          color: AppColors.textSecondary,
                          fontSize: 12,
                        ),
                      ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAmenitiesSection() {
    return Wrap(
      spacing: 8,
      runSpacing: 4,
      children: trip.amenities.map((amenity) => _AmenityChip(amenity: amenity)).toList(),
    );
  }

  Widget _buildRulesAndNotesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (trip.rules.isNotEmpty) ...[
          const Row(
            children: [
              Icon(Icons.rule, color: AppColors.warning, size: 16),
              SizedBox(width: 6),
              Text(
                'قوانين الرحلة:',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          ...trip.rules.take(2).map((rule) => Padding(
            padding: const EdgeInsets.only(right: 22),
            child: Text(
              '• $rule',
              style: const TextStyle(
                fontSize: 11,
                color: AppColors.textSecondary,
              ),
            ),
          )),
        ],
        if (trip.notes != null && trip.notes!.isNotEmpty) ...[
          if (trip.rules.isNotEmpty) const SizedBox(height: 8),
          Row(
            children: [
              const Icon(Icons.info_outline, color: AppColors.info, size: 16),
              const SizedBox(width: 6),
              Expanded(
                child: Text(
                  trip.notes!,
                  style: const TextStyle(
                    fontSize: 11,
                    color: AppColors.textSecondary,
                    fontStyle: FontStyle.italic,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  Color _getTripTypeColor(String tripType) {
    switch (tripType) {
      case 'women_only':
        return Colors.pink;
      case 'family_only':
        return Colors.orange;
      case 'men_only':
        return Colors.blue;
      default:
        return AppColors.primary;
    }
  }

  String _getTripTypeText(String tripType) {
    switch (tripType) {
      case 'women_only':
        return 'نسائية';
      case 'family_only':
        return 'عائلية';
      case 'men_only':
        return 'ذكورية';
      default:
        return 'مختلطة';
    }
  }

  void _navigateToTripDetails(BuildContext context) {
    NavigationUtils.pushWithTransition(
      context,
      TripDetailsPage(tripId: trip.id),
      type: TransitionType.slide,
      duration: const Duration(milliseconds: 400),
    );
  }
}

class _InfoItem extends StatelessWidget {
  final IconData icon;
  final String label;
  final Color color;

  const _InfoItem({
    required this.icon,
    required this.label,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            size: 18,
            color: color,
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 11,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

class _AmenityChip extends StatelessWidget {
  final String amenity;

  const _AmenityChip({required this.amenity});

  @override
  Widget build(BuildContext context) {
    final amenityInfo = _getAmenityInfo(amenity);
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: AppColors.info.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.info.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            amenityInfo['icon'],
            size: 12,
            color: AppColors.info,
          ),
          const SizedBox(width: 4),
          Text(
            amenityInfo['text'],
            style: const TextStyle(
              fontSize: 10,
              color: AppColors.info,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Map<String, dynamic> _getAmenityInfo(String amenity) {
    switch (amenity.toLowerCase()) {
      case 'wifi':
        return {'icon': Icons.wifi, 'text': 'واي فاي'};
      case 'ac':
      case 'air_conditioning':
        return {'icon': Icons.ac_unit, 'text': 'تكييف'};
      case 'music':
        return {'icon': Icons.music_note, 'text': 'موسيقى'};
      case 'charging':
        return {'icon': Icons.battery_charging_full, 'text': 'شحن'};
      case 'water':
        return {'icon': Icons.local_drink, 'text': 'ماء'};
      case 'snacks':
        return {'icon': Icons.fastfood, 'text': 'وجبات خفيفة'};
      default:
        return {'icon': Icons.check_circle, 'text': amenity};
    }
  }
}

class CompactTripCard extends StatelessWidget {
  final TripModel trip;
  final VoidCallback? onTap;

  const CompactTripCard({
    super.key,
    required this.trip,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return TripCard(
      trip: trip,
      onTap: onTap,
      showBookButton: false,
      isCompact: true,
    );
  }
}
