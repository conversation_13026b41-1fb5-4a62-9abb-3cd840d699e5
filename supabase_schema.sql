-- Safarni Database Schema for Supabase
-- This file contains the complete database schema for the <PERSON>farni travel app

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table (extends Supabase auth.users)
CREATE TABLE public.users (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    phone TEXT,
    full_name TEXT NOT NULL,
    role TEXT NOT NULL DEFAULT 'traveler' CHECK (role IN ('traveler', 'trip_leader')),
    profile_image_url TEXT,
    bio TEXT,
    city TEXT,
    date_of_birth DATE,
    gender TEXT CHECK (gender IN ('male', 'female')),
    is_verified BOOLEAN DEFAULT FALSE,
    is_leader BOOLEAN DEFAULT FALSE,
    balance DECIMAL(10,2) DEFAULT 0.00,
    rating DECIMAL(3,2) DEFAULT 0.00,
    total_trips INTEGER DEFAULT 0,
    total_ratings INTEGER DEFAULT 0,
    badges TEXT[] DEFAULT '{}',
    preferences JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Trip Leader Documents table
CREATE TABLE public.leader_documents (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    license_image_url TEXT NOT NULL,
    license_number TEXT,
    license_expiry_date DATE,
    verification_status TEXT DEFAULT 'pending' CHECK (verification_status IN ('pending', 'approved', 'rejected')),
    verified_at TIMESTAMP WITH TIME ZONE,
    verified_by UUID REFERENCES public.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Vehicles table
CREATE TABLE public.vehicles (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    owner_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    make TEXT NOT NULL,
    model TEXT NOT NULL,
    year INTEGER,
    color TEXT NOT NULL,
    plate_number TEXT NOT NULL,
    image_url TEXT,
    seats INTEGER NOT NULL DEFAULT 4,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Trips table
CREATE TABLE public.trips (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    leader_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    driver_id UUID REFERENCES public.users(id) ON DELETE CASCADE, -- Add driver_id for RLS
    vehicle_id UUID REFERENCES public.vehicles(id),
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    from_city TEXT NOT NULL,
    to_city TEXT NOT NULL,
    departure_date DATE NOT NULL,
    return_date DATE,
    departure_time TIME NOT NULL,
    return_time TIME,
    price DECIMAL(8,2) NOT NULL,
    total_seats INTEGER NOT NULL,
    available_seats INTEGER NOT NULL,
    trip_type TEXT DEFAULT 'mixed' CHECK (trip_type IN ('mixed', 'women_only', 'family_only')),
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'completed', 'cancelled', 'draft', 'published')),
    image_urls TEXT[] DEFAULT '{}',
    rules TEXT[] DEFAULT '{}',
    meeting_point JSONB,
    notes TEXT,
    allow_instant_booking BOOLEAN DEFAULT FALSE,
    booking_deadline TIMESTAMP WITH TIME ZONE,
    rating DECIMAL(3,2) DEFAULT 0.00,
    total_ratings INTEGER DEFAULT 0,
    amenities JSONB DEFAULT '[]',
    -- New fields for enhanced trip functionality
    car_type TEXT,
    car_color TEXT,
    car_plate TEXT,
    is_price_negotiable BOOLEAN DEFAULT FALSE,
    is_instant_booking BOOLEAN DEFAULT FALSE,
    duration_minutes INTEGER,
    trip_program TEXT,
    car_image TEXT,
    route_from TEXT,
    route_to TEXT,
    date_time TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Trip Stops table
CREATE TABLE public.trip_stops (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    trip_id UUID REFERENCES public.trips(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    estimated_time TIME NOT NULL,
    duration_minutes INTEGER DEFAULT 15,
    location JSONB,
    order_index INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Bookings table
CREATE TABLE public.bookings (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    trip_id UUID REFERENCES public.trips(id) ON DELETE CASCADE,
    traveler_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    seats_booked INTEGER NOT NULL DEFAULT 1,
    total_amount DECIMAL(8,2) NOT NULL,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'cancelled', 'completed')),
    payment_status TEXT DEFAULT 'pending' CHECK (payment_status IN ('pending', 'paid', 'refunded')),
    booking_notes TEXT,
    confirmed_at TIMESTAMP WITH TIME ZONE,
    cancelled_at TIMESTAMP WITH TIME ZONE,
    cancellation_reason TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(trip_id, traveler_id)
);

-- Messages table (for trip chat)
CREATE TABLE public.messages (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    trip_id UUID REFERENCES public.trips(id) ON DELETE CASCADE,
    sender_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    message_type TEXT DEFAULT 'text' CHECK (message_type IN ('text', 'image', 'system')),
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Ratings table
CREATE TABLE public.ratings (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    trip_id UUID REFERENCES public.trips(id) ON DELETE CASCADE,
    rater_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    rated_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    comment TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(trip_id, rater_id, rated_id)
);

-- Transactions table (for balance tracking)
CREATE TABLE public.transactions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    trip_id UUID REFERENCES public.trips(id) ON DELETE SET NULL,
    amount DECIMAL(10,2) NOT NULL,
    transaction_type TEXT NOT NULL CHECK (transaction_type IN ('deposit', 'withdrawal', 'commission', 'refund', 'earning')),
    description TEXT,
    status TEXT DEFAULT 'completed' CHECK (status IN ('pending', 'completed', 'failed')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_users_role ON public.users(role);
CREATE INDEX idx_users_is_leader ON public.users(is_leader);
CREATE INDEX idx_trips_leader_id ON public.trips(leader_id);
CREATE INDEX idx_trips_status ON public.trips(status);
CREATE INDEX idx_trips_departure_date ON public.trips(departure_date);
CREATE INDEX idx_trips_from_city ON public.trips(from_city);
CREATE INDEX idx_trips_to_city ON public.trips(to_city);
CREATE INDEX idx_bookings_trip_id ON public.bookings(trip_id);
CREATE INDEX idx_bookings_traveler_id ON public.bookings(traveler_id);
CREATE INDEX idx_bookings_status ON public.bookings(status);
CREATE INDEX idx_messages_trip_id ON public.messages(trip_id);
CREATE INDEX idx_ratings_trip_id ON public.ratings(trip_id);
CREATE INDEX idx_transactions_user_id ON public.transactions(user_id);

-- Enable Row Level Security (RLS)
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.leader_documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.vehicles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.trips ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.trip_stops ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.bookings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.ratings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.transactions ENABLE ROW LEVEL SECURITY;

-- RLS Policies

-- Users policies
CREATE POLICY "Users can view their own profile" ON public.users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can view public profiles" ON public.users
    FOR SELECT USING (true);

-- Leader documents policies
CREATE POLICY "Users can manage their own documents" ON public.leader_documents
    FOR ALL USING (auth.uid() = user_id);

-- Vehicles policies
CREATE POLICY "Users can manage their own vehicles" ON public.vehicles
    FOR ALL USING (auth.uid() = owner_id);

-- Trips policies
CREATE POLICY "Anyone can view published trips" ON public.trips
    FOR SELECT USING (status = 'published');

CREATE POLICY "Anyone can view active trips" ON public.trips
    FOR SELECT USING (status = 'active');

CREATE POLICY "Leaders can manage their own trips" ON public.trips
    FOR ALL USING (auth.uid() = leader_id);

-- Additional policy for driver_id compatibility
CREATE POLICY "Drivers can manage their own trips" ON public.trips
    FOR ALL USING (auth.uid() = driver_id);

-- Policy for inserting trips (ensure driver_id is set correctly)
CREATE POLICY "Authenticated users can create trips" ON public.trips
    FOR INSERT WITH CHECK (auth.uid() = driver_id AND auth.uid() = leader_id);

-- Bookings policies
CREATE POLICY "Users can view their own bookings" ON public.bookings
    FOR SELECT USING (auth.uid() = traveler_id OR auth.uid() = (SELECT leader_id FROM trips WHERE id = trip_id));

CREATE POLICY "Travelers can create bookings" ON public.bookings
    FOR INSERT WITH CHECK (auth.uid() = traveler_id);

CREATE POLICY "Users can update their own bookings" ON public.bookings
    FOR UPDATE USING (auth.uid() = traveler_id OR auth.uid() = (SELECT leader_id FROM trips WHERE id = trip_id));

-- Messages policies
CREATE POLICY "Trip participants can view messages" ON public.messages
    FOR SELECT USING (
        auth.uid() = sender_id OR
        auth.uid() = (SELECT leader_id FROM trips WHERE id = trip_id) OR
        auth.uid() IN (SELECT traveler_id FROM bookings WHERE trip_id = messages.trip_id AND status = 'confirmed')
    );

CREATE POLICY "Trip participants can send messages" ON public.messages
    FOR INSERT WITH CHECK (
        auth.uid() = sender_id AND (
            auth.uid() = (SELECT leader_id FROM trips WHERE id = trip_id) OR
            auth.uid() IN (SELECT traveler_id FROM bookings WHERE trip_id = messages.trip_id AND status = 'confirmed')
        )
    );

-- Ratings policies
CREATE POLICY "Users can view all ratings" ON public.ratings
    FOR SELECT USING (true);

CREATE POLICY "Users can create ratings for completed trips" ON public.ratings
    FOR INSERT WITH CHECK (
        auth.uid() = rater_id AND
        EXISTS (
            SELECT 1 FROM bookings b
            JOIN trips t ON b.trip_id = t.id
            WHERE b.trip_id = ratings.trip_id
            AND (b.traveler_id = auth.uid() OR t.leader_id = auth.uid())
            AND t.status = 'completed'
        )
    );

-- Transactions policies
CREATE POLICY "Users can view their own transactions" ON public.transactions
    FOR SELECT USING (auth.uid() = user_id);

-- Functions and Triggers

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add triggers for updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_leader_documents_updated_at BEFORE UPDATE ON public.leader_documents
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_vehicles_updated_at BEFORE UPDATE ON public.vehicles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_trips_updated_at BEFORE UPDATE ON public.trips
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_bookings_updated_at BEFORE UPDATE ON public.bookings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to automatically create user profile on signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.users (id, email, full_name)
    VALUES (NEW.id, NEW.email, COALESCE(NEW.raw_user_meta_data->>'full_name', 'مستخدم جديد'));
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for new user creation
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Function to update trip available seats when booking is created/updated
CREATE OR REPLACE FUNCTION update_trip_available_seats()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE public.trips
        SET available_seats = available_seats - NEW.seats_booked
        WHERE id = NEW.trip_id;
        RETURN NEW;
    ELSIF TG_OP = 'UPDATE' THEN
        UPDATE public.trips
        SET available_seats = available_seats + OLD.seats_booked - NEW.seats_booked
        WHERE id = NEW.trip_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE public.trips
        SET available_seats = available_seats + OLD.seats_booked
        WHERE id = OLD.trip_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Triggers for booking seat management
CREATE TRIGGER booking_seat_management
    AFTER INSERT OR UPDATE OR DELETE ON public.bookings
    FOR EACH ROW EXECUTE FUNCTION update_trip_available_seats();
