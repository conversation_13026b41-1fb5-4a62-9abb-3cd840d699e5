import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import '../../constants/app_theme.dart';
import '../../constants/app_constants.dart';
import '../../providers/auth_provider.dart';
import '../../providers/trip_provider.dart';
import '../../models/trip_model.dart';
import '../../widgets/custom_button.dart';
import '../../widgets/custom_text_field.dart';
import '../../utils/navigation_utils.dart';
import '../../utils/navigation_utils.dart';

class CreateTripPage extends StatefulWidget {
  const CreateTripPage({super.key});

  @override
  State<CreateTripPage> createState() => _CreateTripPageState();
}

class _CreateTripPageState extends State<CreateTripPage> {
  final _formKey = GlobalKey<FormState>();
  final _pageController = PageController();
  int _currentStep = 0;
  final int _totalSteps = 8;

  // Form controllers
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _fromCityController = TextEditingController();
  final _toCityController = TextEditingController();
  final _priceController = TextEditingController();
  final _totalSeatsController = TextEditingController();
  final _rulesController = TextEditingController();
  final _notesController = TextEditingController();
  final _carModelController = TextEditingController();
  final _carColorController = TextEditingController();
  final _carPlateController = TextEditingController();

  // Form data
  String _tripType = AppConstants.tripTypeMixed;
  DateTime? _departureDate;
  TimeOfDay? _departureTime;
  DateTime? _returnDate;
  TimeOfDay? _returnTime;
  List<String> _selectedImagePaths = [];
  bool _allowInstantBooking = false;
  bool _isPriceNegotiable = false;
  List<String> _amenities = [];
  List<TripStop> _stops = [];

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _fromCityController.dispose();
    _toCityController.dispose();
    _priceController.dispose();
    _totalSeatsController.dispose();
    _rulesController.dispose();
    _notesController.dispose();
    _carModelController.dispose();
    _carColorController.dispose();
    _carPlateController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('إنشاء رحلة جديدة'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Column(
        children: [
          // Progress Indicator
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'الخطوة ${_currentStep + 1} من $_totalSteps',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      '${((_currentStep + 1) / _totalSteps * 100).round()}%',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: AppColors.primary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                LinearProgressIndicator(
                  value: (_currentStep + 1) / _totalSteps,
                  backgroundColor: AppColors.surfaceVariant,
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                  borderRadius: BorderRadius.circular(4),
                ),
              ],
            ),
          ),

          // Form Steps
          Expanded(
            child: PageView(
              controller: _pageController,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                _buildTripTypeStep(),
                _buildRouteStep(),
                _buildDateTimeStep(),
                _buildVehicleStep(),
                _buildSeatsAndPriceStep(),
                _buildRulesStep(),
                _buildAmenitiesStep(),
                _buildPreviewStep(),
              ],
            ),
          ),

          // Navigation Buttons
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 4,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: Row(
              children: [
                if (_currentStep > 0)
                  Expanded(
                    child: OutlinedButton(
                      onPressed: _previousStep,
                      child: const Text('السابق'),
                    ),
                  ),
                if (_currentStep > 0) const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _currentStep < _totalSteps - 1
                        ? _nextStep
                        : _publishTrip,
                    child: Text(_currentStep < _totalSteps - 1
                        ? 'التالي'
                        : 'نشر الرحلة'),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTripTypeStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'نوع الرحلة',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 8),
          Text(
            'اختر نوع الرحلة المناسب',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: AppColors.textSecondary,
                ),
          ),
          const SizedBox(height: 24),
          _buildTripTypeCard(
            type: AppConstants.tripTypeMixed,
            title: 'رحلة مختلطة',
            subtitle: 'مفتوحة للجميع - رجال ونساء',
            icon: Icons.groups,
            color: AppColors.primary,
          ),
          const SizedBox(height: 16),
          _buildTripTypeCard(
            type: AppConstants.tripTypeWomenOnly,
            title: 'رحلة نسائية فقط',
            subtitle: 'مخصصة للنساء فقط',
            icon: Icons.woman,
            color: Colors.pink,
          ),
          const SizedBox(height: 16),
          _buildTripTypeCard(
            type: AppConstants.tripTypeFamilyOnly,
            title: 'رحلة عائلية',
            subtitle: 'مخصصة للعائلات والأطفال',
            icon: Icons.family_restroom,
            color: Colors.green,
          ),
        ],
      ),
    );
  }

  Widget _buildTripTypeCard({
    required String type,
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
  }) {
    final isSelected = _tripType == type;

    return Card(
      elevation: isSelected ? 8 : 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: isSelected ? color : Colors.transparent,
          width: 2,
        ),
      ),
      child: InkWell(
        onTap: () {
          setState(() {
            _tripType = type;
          });
        },
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Row(
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(30),
                ),
                child: Icon(
                  icon,
                  size: 30,
                  color: color,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: isSelected ? color : AppColors.textPrimary,
                          ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: AppColors.textSecondary,
                          ),
                    ),
                  ],
                ),
              ),
              if (isSelected)
                Icon(
                  Icons.check_circle,
                  color: color,
                  size: 24,
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRouteStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'مسار الرحلة',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              'حدد نقطة الانطلاق والوجهة',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: AppColors.textSecondary,
                  ),
            ),
            const SizedBox(height: 24),
            CustomTextField(
              controller: _titleController,
              label: 'عنوان الرحلة',
              prefixIcon: Icons.title,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'عنوان الرحلة مطلوب';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            CustomTextField(
              controller: _fromCityController,
              label: 'من (المدينة)',
              prefixIcon: Icons.location_on,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'مدينة الانطلاق مطلوبة';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            CustomTextField(
              controller: _toCityController,
              label: 'إلى (المدينة)',
              prefixIcon: Icons.location_on,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'مدينة الوجهة مطلوبة';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            CustomTextField(
              controller: _descriptionController,
              label: 'وصف الرحلة',
              prefixIcon: Icons.description,
              maxLines: 3,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'وصف الرحلة مطلوب';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDateTimeStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'التاريخ والوقت',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 8),
          Text(
            'حدد تاريخ ووقت الرحلة',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: AppColors.textSecondary,
                ),
          ),
          const SizedBox(height: 24),

          // Departure Date
          Card(
            child: ListTile(
              leading: Icon(Icons.calendar_today, color: AppColors.primary),
              title: const Text('تاريخ الانطلاق'),
              subtitle: Text(
                _departureDate != null
                    ? '${_departureDate!.day}/${_departureDate!.month}/${_departureDate!.year}'
                    : 'اختر التاريخ',
              ),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () async {
                final date = await showDatePicker(
                  context: context,
                  initialDate: DateTime.now().add(const Duration(days: 1)),
                  firstDate: DateTime.now(),
                  lastDate: DateTime.now().add(const Duration(days: 365)),
                );
                if (date != null) {
                  setState(() {
                    _departureDate = date;
                  });
                }
              },
            ),
          ),
          const SizedBox(height: 16),

          // Departure Time
          Card(
            child: ListTile(
              leading: Icon(Icons.access_time, color: AppColors.primary),
              title: const Text('وقت الانطلاق'),
              subtitle: Text(
                _departureTime != null
                    ? '${_departureTime!.hour.toString().padLeft(2, '0')}:${_departureTime!.minute.toString().padLeft(2, '0')}'
                    : 'اختر الوقت',
              ),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () async {
                final time = await showTimePicker(
                  context: context,
                  initialTime: TimeOfDay.now(),
                );
                if (time != null) {
                  setState(() {
                    _departureTime = time;
                  });
                }
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVehicleStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'معلومات المركبة',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 8),
          Text(
            'أضف صور ومعلومات مركبتك',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: AppColors.textSecondary,
                ),
          ),
          const SizedBox(height: 24),

          // Vehicle Photos
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.photo_camera, color: AppColors.primary),
                      const SizedBox(width: 8),
                      const Text(
                        'صور المركبة',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  if (_selectedImagePaths.isEmpty)
                    GestureDetector(
                      onTap: _pickImages,
                      child: Container(
                        height: 120,
                        width: double.infinity,
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: AppColors.border,
                            style: BorderStyle.solid,
                          ),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.add_photo_alternate,
                              size: 48,
                              color: AppColors.textTertiary,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'اضغط لإضافة صور',
                              style: TextStyle(color: AppColors.textSecondary),
                            ),
                          ],
                        ),
                      ),
                    )
                  else
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: _selectedImagePaths.map((path) {
                        return Stack(
                          children: [
                            Container(
                              width: 80,
                              height: 80,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(color: AppColors.border),
                              ),
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(8),
                                child: Icon(
                                  Icons.image,
                                  size: 40,
                                  color: AppColors.textTertiary,
                                ),
                              ),
                            ),
                            Positioned(
                              top: 4,
                              right: 4,
                              child: GestureDetector(
                                onTap: () {
                                  setState(() {
                                    _selectedImagePaths.remove(path);
                                  });
                                },
                                child: Container(
                                  width: 20,
                                  height: 20,
                                  decoration: const BoxDecoration(
                                    color: Colors.red,
                                    shape: BoxShape.circle,
                                  ),
                                  child: const Icon(
                                    Icons.close,
                                    size: 14,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        );
                      }).toList(),
                    ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          CustomTextField(
            controller: _carModelController,
            label: 'نوع المركبة',
            prefixIcon: Icons.directions_car,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'نوع المركبة مطلوب';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),

          CustomTextField(
            controller: _carColorController,
            label: 'لون المركبة',
            prefixIcon: Icons.palette,
          ),
          const SizedBox(height: 16),

          CustomTextField(
            controller: _carPlateController,
            label: 'رقم اللوحة',
            prefixIcon: Icons.confirmation_number,
          ),
        ],
      ),
    );
  }

  void _pickImages() async {
    final ImagePicker picker = ImagePicker();
    final List<XFile> images = await picker.pickMultiImage();

    setState(() {
      _selectedImagePaths.addAll(images.map((image) => image.path));
    });
  }

  Widget _buildSeatsAndPriceStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'المقاعد والسعر',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 8),
          Text(
            'حدد عدد المقاعد والسعر',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: AppColors.textSecondary,
                ),
          ),
          const SizedBox(height: 24),
          CustomTextField(
            controller: _totalSeatsController,
            label: 'عدد المقاعد المتاحة',
            prefixIcon: Icons.airline_seat_recline_normal,
            keyboardType: TextInputType.number,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'عدد المقاعد مطلوب';
              }
              final seats = int.tryParse(value);
              if (seats == null || seats <= 0) {
                return 'عدد المقاعد يجب أن يكون أكبر من صفر';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          CustomTextField(
            controller: _priceController,
            label: 'السعر للمقعد الواحد (درهم)',
            prefixIcon: Icons.attach_money,
            keyboardType: TextInputType.number,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'السعر مطلوب';
              }
              final price = double.tryParse(value);
              if (price == null || price <= 0) {
                return 'السعر يجب أن يكون أكبر من صفر';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          Card(
            child: SwitchListTile(
              title: const Text('السعر قابل للتفاوض'),
              subtitle: const Text('السماح للمسافرين بالتفاوض على السعر'),
              value: _isPriceNegotiable,
              onChanged: (value) {
                setState(() {
                  _isPriceNegotiable = value;
                });
              },
            ),
          ),
          const SizedBox(height: 16),
          Card(
            child: SwitchListTile(
              title: const Text('الحجز الفوري'),
              subtitle: const Text('السماح بالحجز الفوري دون موافقة'),
              value: _allowInstantBooking,
              onChanged: (value) {
                setState(() {
                  _allowInstantBooking = value;
                });
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRulesStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'قوانين الرحلة',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 8),
          Text(
            'حدد قوانين وشروط الرحلة',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: AppColors.textSecondary,
                ),
          ),
          const SizedBox(height: 24),
          CustomTextField(
            controller: _rulesController,
            label: 'قوانين الرحلة',
            prefixIcon: Icons.rule,
            maxLines: 5,
            hint:
                'مثال:\n- عدم التدخين\n- الالتزام بالمواعيد\n- احترام الركاب الآخرين',
          ),
          const SizedBox(height: 16),
          CustomTextField(
            controller: _notesController,
            label: 'ملاحظات إضافية',
            prefixIcon: Icons.note,
            maxLines: 3,
            hint: 'أي ملاحظات أو تعليمات إضافية للمسافرين',
          ),
        ],
      ),
    );
  }

  Widget _buildAmenitiesStep() {
    final availableAmenities = [
      {'id': 'wifi', 'name': 'واي فاي', 'icon': Icons.wifi},
      {'id': 'ac', 'name': 'تكييف', 'icon': Icons.ac_unit},
      {'id': 'music', 'name': 'موسيقى', 'icon': Icons.music_note},
      {
        'id': 'charging',
        'name': 'شحن الهاتف',
        'icon': Icons.battery_charging_full
      },
      {'id': 'water', 'name': 'مياه', 'icon': Icons.local_drink},
      {'id': 'snacks', 'name': 'وجبات خفيفة', 'icon': Icons.fastfood},
    ];

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'المرافق والخدمات',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 8),
          Text(
            'اختر المرافق المتوفرة في رحلتك',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: AppColors.textSecondary,
                ),
          ),
          const SizedBox(height: 24),
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              childAspectRatio: 1.2,
            ),
            itemCount: availableAmenities.length,
            itemBuilder: (context, index) {
              final amenity = availableAmenities[index];
              final isSelected = _amenities.contains(amenity['id']);

              return Card(
                elevation: isSelected ? 8 : 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                  side: BorderSide(
                    color: isSelected ? AppColors.primary : Colors.transparent,
                    width: 2,
                  ),
                ),
                child: InkWell(
                  onTap: () {
                    setState(() {
                      if (isSelected) {
                        _amenities.remove(amenity['id']);
                      } else {
                        _amenities.add(amenity['id'] as String);
                      }
                    });
                  },
                  borderRadius: BorderRadius.circular(16),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          amenity['icon'] as IconData,
                          size: 32,
                          color: isSelected
                              ? AppColors.primary
                              : AppColors.textSecondary,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          amenity['name'] as String,
                          style:
                              Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    fontWeight: isSelected
                                        ? FontWeight.bold
                                        : FontWeight.normal,
                                    color: isSelected
                                        ? AppColors.primary
                                        : AppColors.textPrimary,
                                  ),
                          textAlign: TextAlign.center,
                        ),
                        if (isSelected)
                          const Icon(
                            Icons.check_circle,
                            color: AppColors.primary,
                            size: 16,
                          ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildPreviewStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'معاينة الرحلة',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 8),
          Text(
            'راجع تفاصيل رحلتك قبل النشر',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: AppColors.textSecondary,
                ),
          ),
          const SizedBox(height: 24),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _titleController.text,
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Icon(Icons.location_on,
                          color: AppColors.primary, size: 20),
                      const SizedBox(width: 8),
                      Text(
                        '${_fromCityController.text} ← ${_toCityController.text}',
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  color: AppColors.primary,
                                ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildPreviewRow('نوع الرحلة', _getTripTypeText()),
                  _buildPreviewRow(
                      'التاريخ',
                      _departureDate != null
                          ? '${_departureDate!.day}/${_departureDate!.month}/${_departureDate!.year}'
                          : 'غير محدد'),
                  _buildPreviewRow(
                      'الوقت',
                      _departureTime != null
                          ? '${_departureTime!.hour.toString().padLeft(2, '0')}:${_departureTime!.minute.toString().padLeft(2, '0')}'
                          : 'غير محدد'),
                  _buildPreviewRow('عدد المقاعد', _totalSeatsController.text),
                  _buildPreviewRow('السعر', '${_priceController.text} درهم'),
                  _buildPreviewRow('نوع المركبة', _carModelController.text),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPreviewRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColors.textSecondary,
                  ),
            ),
          ),
          Text(
            value.isEmpty ? 'غير محدد' : value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
        ],
      ),
    );
  }

  String _getTripTypeText() {
    switch (_tripType) {
      case AppConstants.tripTypeMixed:
        return 'رحلة مختلطة';
      case AppConstants.tripTypeWomenOnly:
        return 'رحلة نسائية فقط';
      case AppConstants.tripTypeFamilyOnly:
        return 'رحلة عائلية';
      default:
        return 'غير محدد';
    }
  }

  void _nextStep() {
    if (_currentStep < _totalSteps - 1) {
      if (_validateCurrentStep()) {
        setState(() {
          _currentStep++;
        });
        _pageController.nextPage(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    }
  }

  void _previousStep() {
    if (_currentStep > 0) {
      setState(() {
        _currentStep--;
      });
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  bool _validateCurrentStep() {
    switch (_currentStep) {
      case 0: // Trip type
        return _tripType.isNotEmpty;
      case 1: // Route
        return _formKey.currentState?.validate() ?? false;
      case 2: // Date & Time
        if (_departureDate == null) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('يرجى اختيار تاريخ الانطلاق')),
          );
          return false;
        }
        if (_departureTime == null) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('يرجى اختيار وقت الانطلاق')),
          );
          return false;
        }
        return true;
      case 3: // Vehicle
        if (_carModelController.text.isEmpty) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('يرجى إدخال نوع المركبة')),
          );
          return false;
        }
        return true;
      case 4: // Seats & Price
        if (_totalSeatsController.text.isEmpty ||
            _priceController.text.isEmpty) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('يرجى إدخال عدد المقاعد والسعر')),
          );
          return false;
        }
        return true;
      default:
        return true;
    }
  }

  void _publishTrip() async {
    if (!_validateCurrentStep()) return;

    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final tripProvider = Provider.of<TripProvider>(context, listen: false);

    if (authProvider.currentUser == null) return;

    // Check if user has sufficient balance
    final user = authProvider.currentUser!;
    if (!user.canCreateTrips) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'تحتاج إلى رصيد 5 درهم على الأقل لإنشاء رحلات جديدة. رصيدك الحالي: ${user.displayBalance}',
          ),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 4),
        ),
      );
      return;
    }

    // Calculate duration if return date/time is set
    int? durationMinutes;
    if (_returnDate != null && _returnTime != null) {
      final returnDateTime = DateTime(
        _returnDate!.year,
        _returnDate!.month,
        _returnDate!.day,
        _returnTime!.hour,
        _returnTime!.minute,
      );
      final departureDateTime = DateTime(
        _departureDate!.year,
        _departureDate!.month,
        _departureDate!.day,
        _departureTime!.hour,
        _departureTime!.minute,
      );
      durationMinutes = returnDateTime.difference(departureDateTime).inMinutes;
    }

    final trip = TripModel(
      id: '',
      leaderId: authProvider.currentUser!.id,
      title: _titleController.text,
      description: _descriptionController.text,
      fromCity: _fromCityController.text,
      toCity: _toCityController.text,
      departureDate: _departureDate!,
      returnDate: _returnDate,
      departureTime:
          '${_departureTime!.hour.toString().padLeft(2, '0')}:${_departureTime!.minute.toString().padLeft(2, '0')}',
      returnTime: _returnTime != null
          ? '${_returnTime!.hour.toString().padLeft(2, '0')}:${_returnTime!.minute.toString().padLeft(2, '0')}'
          : null,
      price: double.parse(_priceController.text),
      totalSeats: int.parse(_totalSeatsController.text),
      availableSeats: int.parse(_totalSeatsController.text),
      tripType: _tripType,
      status: 'published', // Set status to published for immediate visibility
      imageUrls: _selectedImagePaths,
      carModel: _carModelController.text.isNotEmpty ? _carModelController.text : null,
      carColor: _carColorController.text.isNotEmpty ? _carColorController.text : null,
      carPlateNumber: _carPlateController.text.isNotEmpty ? _carPlateController.text : null,
      rules: _rulesController.text
          .split('\n')
          .where((rule) => rule.trim().isNotEmpty)
          .toList(),
      notes: _notesController.text.isNotEmpty ? _notesController.text : null,
      allowInstantBooking: _allowInstantBooking,
      amenities: _amenities,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      // New fields
      carType: _carModelController.text.isNotEmpty ? _carModelController.text : null,
      carPlate: _carPlateController.text.isNotEmpty ? _carPlateController.text : null,
      isPriceNegotiable: _isPriceNegotiable,
      isInstantBooking: _allowInstantBooking,
      durationMinutes: durationMinutes,
      tripProgram: _descriptionController.text.isNotEmpty ? _descriptionController.text : null,
      carImage: _selectedImagePaths.isNotEmpty ? _selectedImagePaths.first : null,
      routeFrom: _fromCityController.text,
      routeTo: _toCityController.text,
      dateTime: DateTime(
        _departureDate!.year,
        _departureDate!.month,
        _departureDate!.day,
        _departureTime!.hour,
        _departureTime!.minute,
      ),
    );

    final success = await tripProvider.createTrip(trip);

    if (success && mounted) {
      // Refresh user trips to show the new trip immediately
      await tripProvider.loadUserTrips(authProvider.currentUser!.id);

      // Deduct 5% commission from user balance
      const commissionRate = 0.05;
      final commissionAmount = trip.price * commissionRate;
      final newBalance = user.balance - commissionAmount;

      // Update user balance
      final updatedUser = user.copyWith(balance: newBalance);
      await authProvider.updateProfile(updatedUser);

      // Show success animation with commission info
      if (mounted) {
        NavigationUtils.showSuccessAnimation(context,
            'تم نشر الرحلة بنجاح!\nتم خصم ${commissionAmount.toStringAsFixed(2)} درهم كعمولة');
      }

      // Wait for animation to complete then navigate back
      await Future.delayed(const Duration(milliseconds: 3000));
      if (mounted) {
        Navigator.pop(context);
      }
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('فشل في نشر الرحلة'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
