import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/user_model.dart';
import '../services/supabase_service.dart';

class AuthProvider extends ChangeNotifier {
  UserModel? _currentUser;
  bool _isLoading = false;
  String? _error;

  // Getters
  UserModel? get currentUser => _currentUser;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isAuthenticated => _currentUser != null;
  bool get isTripLeader => _currentUser?.isTripLeader ?? false;
  bool get isTraveler => _currentUser?.isTraveler ?? false;

  AuthProvider() {
    _initializeAuth();
  }

  void _initializeAuth() {
    // Listen to auth state changes
    SupabaseService.authStateChanges.listen((data) {
      final user = data.session?.user;
      if (user != null) {
        _loadUserProfile(user.id);
      } else {
        _currentUser = null;
        notifyListeners();
      }
    });

    // Check if user is already logged in
    final currentUser = SupabaseService.currentUser;
    if (currentUser != null) {
      _loadUserProfile(currentUser.id);
    }
  }

  Future<void> _loadUserProfile(String userId) async {
    try {
      final userProfile = await SupabaseService.getUserProfile(userId);
      _currentUser = userProfile;
      _clearError();
      notifyListeners();
    } catch (e) {
      _setError('فشل في تحميل بيانات المستخدم');
      if (kDebugMode) {
        print('Load user profile error: $e');
      }
    }
  }

  Future<bool> signUp({
    required String password,
    required String fullName,
    required String role,
    required String phone,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await SupabaseService.signUpWithPhone(
        phone: phone,
        password: password,
        fullName: fullName,
      );

      if (response['success']) {
        await _loadUserProfile(response['userId']);
        _setLoading(false);
        return true;
      } else {
        _setError('فشل في إنشاء الحساب');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('فشل في إنشاء الحساب: ${e.toString()}');
      _setLoading(false);
      if (kDebugMode) {
        print('Signup error: $e');
      }
      return false;
    }
  }

  Future<bool> signIn({
    required String phone,
    required String password,
    String? userType,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await SupabaseService.signInWithPhone(
        phone: phone,
        password: password,
      );

      if (response['success']) {
        await _loadUserProfile(response['userId']);
        _setLoading(false);
        return true;
      } else {
        _setError('فشل في تسجيل الدخول');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('فشل في تسجيل الدخول: ${e.toString()}');
      _setLoading(false);
      if (kDebugMode) {
        print('Signin error: $e');
      }
      return false;
    }
  }

  Future<void> signOut() async {
    _setLoading(true);
    _clearError();

    try {
      await SupabaseService.signOut();
      _currentUser = null;
      _setLoading(false);
    } catch (e) {
      _setError('فشل في تسجيل الخروج');
      _setLoading(false);
      if (kDebugMode) {
        print('Signout error: $e');
      }
    }
  }

  Future<bool> updateProfile(UserModel updatedUser) async {
    _setLoading(true);
    _clearError();

    try {
      await SupabaseService.updateUserProfile(updatedUser);
      _currentUser = updatedUser;
      _setLoading(false);
      return true;
    } catch (e) {
      _setError('فشل في تحديث الملف الشخصي');
      _setLoading(false);
      if (kDebugMode) {
        print('Update profile error: $e');
      }
      return false;
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }

  void clearError() {
    _clearError();
  }

  // Validation helpers
  bool isValidPassword(String password) {
    return password.length >= 6;
  }

  bool isValidMoroccanPhone(String phone) {
    final cleanPhone = phone.replaceAll(RegExp(r'[\s\-\(\)]'), '');
    return RegExp(r'^(\+212|0)(6|7)[0-9]{8}$').hasMatch(cleanPhone);
  }

  String? validatePassword(String? password) {
    if (password == null || password.isEmpty) {
      return 'كلمة المرور مطلوبة';
    }
    if (!isValidPassword(password)) {
      return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    }
    return null;
  }

  String? validateFullName(String? fullName) {
    if (fullName == null || fullName.isEmpty) {
      return 'الاسم الكامل مطلوب';
    }
    if (fullName.length < 2) {
      return 'الاسم يجب أن يكون حرفين على الأقل';
    }
    return null;
  }

  String? validatePhone(String? phone) {
    if (phone != null && phone.isNotEmpty && !isValidMoroccanPhone(phone)) {
      return 'رقم الهاتف غير صحيح';
    }
    return null;
  }

  String? validatePhoneRequired(String? phone) {
    if (phone == null || phone.isEmpty) {
      return 'رقم الهاتف مطلوب';
    }
    if (!isValidMoroccanPhone(phone)) {
      return 'رقم الهاتف غير صحيح';
    }
    return null;
  }

  String? validateConfirmPassword(String? password, String? confirmPassword) {
    if (confirmPassword == null || confirmPassword.isEmpty) {
      return 'تأكيد كلمة المرور مطلوب';
    }
    if (password != confirmPassword) {
      return 'كلمة المرور غير متطابقة';
    }
    return null;
  }
}
