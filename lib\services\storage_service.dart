import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:image_picker/image_picker.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:uuid/uuid.dart';

class StorageService {
  static final SupabaseClient _supabase = Supabase.instance.client;
  static const String _driverLicensesBucket = 'driver-licenses'; // Private bucket
  static const String _profileImagesBucket = 'profile-images';   // Public bucket
  static const String _tripGalleryBucket = 'trip_gallery';

  /// Upload profile image to public bucket
  static Future<String?> uploadProfileImage({
    required XFile imageFile,
    required String userId,
  }) async {
    try {
      // Get current user to ensure authentication
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) {
        if (kDebugMode) {
          print('Profile upload failed: User not authenticated');
        }
        return null;
      }

      // Security check: user can only upload their own profile
      if (currentUser.id != userId) {
        if (kDebugMode) {
          print('Profile upload failed: User ID mismatch');
        }
        return null;
      }

      // Construct file path: users/profile_<userID>.<extension>
      final fileExtension = imageFile.name.split('.').last.toLowerCase();
      final fileName = 'profile_$userId.$fileExtension';
      final fullPath = 'users/$fileName';

      if (kDebugMode) {
        print('Profile upload path: $fullPath');
        print('Bucket: $_profileImagesBucket (public)');
      }

      // Read image bytes
      final Uint8List imageBytes = await imageFile.readAsBytes();

      // Determine content type
      String contentType;
      switch (fileExtension) {
        case 'jpg':
        case 'jpeg':
          contentType = 'image/jpeg';
          break;
        case 'png':
          contentType = 'image/png';
          break;
        case 'webp':
          contentType = 'image/webp';
          break;
        default:
          contentType = 'image/jpeg';
      }

      // Upload to public bucket (no metadata needed for public files)
      final uploadOptions = FileOptions(
        cacheControl: '3600',
        upsert: true,
        contentType: contentType,
      );

      final response = await _supabase.storage
          .from(_profileImagesBucket)
          .uploadBinary(fullPath, imageBytes, fileOptions: uploadOptions);

      if (response.isNotEmpty) {
        // Generate public URL
        final publicUrl = _supabase.storage
            .from(_profileImagesBucket)
            .getPublicUrl(fullPath);

        if (kDebugMode) {
          print('Profile image uploaded successfully!');
          print('Public URL: $publicUrl');
        }

        return publicUrl;
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error uploading profile image: $e');
      }
      return null;
    }
  }

  /// Upload an image to Supabase Storage with proper access control
  /// This method works for both web and mobile platforms
  static Future<String?> uploadImage({
    required XFile imageFile,
    required String bucket,
    String? folder,
    String? customFileName,
    String? userId,
    bool isPrivate = false,
  }) async {
    try {
      // Generate unique filename
      final uuid = const Uuid();
      final fileExtension = imageFile.name.split('.').last.toLowerCase();
      final fileName = customFileName ?? '${uuid.v4()}.$fileExtension';
      final fullPath = folder != null ? '$folder/$fileName' : fileName;

      // Read image bytes (works for both web and mobile)
      final Uint8List imageBytes = await imageFile.readAsBytes();

      // Prepare upload options
      const uploadOptions = FileOptions(
        cacheControl: '3600',
        upsert: true,
      );

      // Upload to Supabase Storage
      final response = await _supabase.storage
          .from(bucket)
          .uploadBinary(fullPath, imageBytes, fileOptions: uploadOptions);

      if (response.isNotEmpty) {
        // Return the file path for private files, public URL for public files
        if (isPrivate) {
          return fullPath; // Return path for signed URL generation
        } else {
          // Get public URL for public files
          final publicUrl = _supabase.storage
              .from(bucket)
              .getPublicUrl(fullPath);

          return publicUrl;
        }
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error uploading image: $e');
      }
      return null;
    }
  }



  /// Upload driver license image to private bucket
  static Future<String?> uploadDriverLicense({
    required XFile imageFile,
    required String userId,
  }) async {
    try {
      // Get current user to ensure authentication
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) {
        if (kDebugMode) {
          print('License upload failed: User not authenticated');
        }
        return null;
      }

      // Security check: user can only upload their own license
      if (currentUser.id != userId) {
        if (kDebugMode) {
          print('License upload failed: User ID mismatch');
        }
        return null;
      }

      // Construct file path: licenses/license_<userID>.<extension>
      final fileExtension = imageFile.name.split('.').last.toLowerCase();
      final fileName = 'license_$userId.$fileExtension';
      final fullPath = 'licenses/$fileName';

      if (kDebugMode) {
        print('License upload path: $fullPath');
        print('Bucket: $_driverLicensesBucket (private)');
        print('User ID: ${currentUser.id}');
      }

      // Read image bytes
      final Uint8List imageBytes = await imageFile.readAsBytes();

      // Determine content type
      String contentType;
      switch (fileExtension) {
        case 'jpg':
        case 'jpeg':
          contentType = 'image/jpeg';
          break;
        case 'png':
          contentType = 'image/png';
          break;
        case 'webp':
          contentType = 'image/webp';
          break;
        default:
          contentType = 'image/jpeg';
      }

      if (kDebugMode) {
        print('Content type: $contentType');
        print('File size: ${imageBytes.length} bytes');
      }

      // Upload the file
      final uploadOptions = FileOptions(
        cacheControl: '3600',
        upsert: true,
        contentType: contentType,
      );

      if (kDebugMode) {
        print('Starting upload to Supabase...');
      }

      final response = await _supabase.storage
          .from(_driverLicensesBucket)
          .uploadBinary(fullPath, imageBytes, fileOptions: uploadOptions);

      if (response.isEmpty) {
        if (kDebugMode) {
          print('Upload failed: Empty response from Supabase');
        }
        return null;
      }

      if (kDebugMode) {
        print('File uploaded successfully!');
        print('Path: $fullPath');
        print('Driver license upload completed successfully');
      }

      // Return path for database storage
      return fullPath;

    } catch (e) {
      if (kDebugMode) {
        print('Error uploading driver license: $e');
        if (e is StorageException) {
          print('Status code: ${e.statusCode}');
          print('Message: ${e.message}');
          print('Error details: ${e.error}');
        }
      }
      return null;
    }
  }

  /// Upload car image to public bucket
  static Future<String?> uploadCarImage({
    required XFile imageFile,
    required String userId,
  }) async {
    return await uploadImage(
      imageFile: imageFile,
      bucket: _profileImagesBucket,
      folder: 'vehicles',
      customFileName: 'vehicle_$userId.${imageFile.name.split('.').last}',
      userId: userId,
      isPrivate: false,
    );
  }

  /// Upload trip gallery image
  static Future<String?> uploadTripImage({
    required XFile imageFile,
    required String tripId,
    int? imageIndex,
  }) async {
    final fileName = imageIndex != null 
        ? 'trip_${tripId}_$imageIndex.${imageFile.name.split('.').last}'
        : null;
    
    return await uploadImage(
      imageFile: imageFile,
      bucket: _tripGalleryBucket,
      folder: 'trips',
      customFileName: fileName,
    );
  }

  /// Delete an image from storage
  static Future<bool> deleteImage({
    required String bucket,
    required String filePath,
  }) async {
    try {
      await _supabase.storage
          .from(bucket)
          .remove([filePath]);
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Error deleting image: $e');
      }
      return false;
    }
  }

  /// Get file path from public URL
  static String? getFilePathFromUrl(String publicUrl, String bucket) {
    try {
      final uri = Uri.parse(publicUrl);
      final pathSegments = uri.pathSegments;
      final bucketIndex = pathSegments.indexOf(bucket);
      
      if (bucketIndex != -1 && bucketIndex < pathSegments.length - 1) {
        return pathSegments.sublist(bucketIndex + 1).join('/');
      }
      
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error parsing URL: $e');
      }
      return null;
    }
  }

  /// Get profile image public URL for a user
  static String? getProfileImageUrl(String userId, {String? storedUrl}) {
    try {
      // If we have a stored URL, use it
      if (storedUrl != null && storedUrl.isNotEmpty) {
        return storedUrl;
      }

      // Generate public URL for profile image
      // Try different file extensions
      final extensions = ['jpg', 'jpeg', 'png', 'webp'];
      for (final ext in extensions) {
        final filePath = 'users/profile_$userId.$ext';
        final url = _supabase.storage
            .from(_profileImagesBucket)
            .getPublicUrl(filePath);

        if (url.isNotEmpty) {
          if (kDebugMode) {
            print('Generated profile URL: $url');
          }
          return url;
        }
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting profile image URL: $e');
      }
      return null;
    }
  }

  /// Generate public URL for a specific profile image path
  static String getProfileImagePublicUrl(String userId, String extension) {
    final filePath = 'users/profile_$userId.$extension';
    return _supabase.storage
        .from(_profileImagesBucket)
        .getPublicUrl(filePath);
  }





  /// Get car image URL for a user (using public profile-images bucket)
  static String? getCarImageUrl(String userId) {
    try {
      // Try different file extensions in profile-images/vehicles/
      final extensions = ['jpg', 'jpeg', 'png', 'webp'];
      for (final ext in extensions) {
        final filePath = 'vehicles/vehicle_$userId.$ext';
        final url = _supabase.storage
            .from(_profileImagesBucket)
            .getPublicUrl(filePath);

        if (url.isNotEmpty) {
          return url;
        }
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting car image URL: $e');
      }
      return null;
    }
  }

  /// Check if an image exists in storage
  static Future<bool> imageExists({
    required String bucket,
    required String filePath,
  }) async {
    try {
      final pathParts = filePath.split('/');
      final folder = pathParts.length > 1 ? pathParts.first : '';
      final fileName = pathParts.last;

      final response = await _supabase.storage
          .from(bucket)
          .list(path: folder);

      return response.any((file) => file.name == fileName);
    } catch (e) {
      if (kDebugMode) {
        print('Error checking image existence: $e');
      }
      return false;
    }
  }

  /// Check if storage buckets exist and are accessible
  static Future<bool> checkStorageHealth() async {
    try {
      // Try to list files in each bucket to verify access
      await _supabase.storage.from(_driverLicensesBucket).list();
      await _supabase.storage.from(_profileImagesBucket).list();
      await _supabase.storage.from(_tripGalleryBucket).list();
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Storage health check failed: $e');
      }
      return false;
    }
  }
}
